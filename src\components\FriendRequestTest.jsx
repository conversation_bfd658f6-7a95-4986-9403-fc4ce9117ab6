import React, { useState } from 'react';
import FriendRequestModal from './FriendRequestModal';

/**
 * Test component for Friend Request Modal functionality
 * This component allows testing the modal without needing actual notifications
 */
export default function FriendRequestTest() {
  const [showModal, setShowModal] = useState(false);
  const [testData, setTestData] = useState({
    requestId: 'test_request_123',
    fromUserId: 'test_user_456'
  });

  const handleOpenModal = () => {
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  const handleRequestHandled = (action, request) => {
    console.log(`Test: Friend request ${action}:`, request);
    alert(`Friend request ${action} successfully!`);
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      zIndex: 9999,
      background: 'white',
      padding: '10px',
      border: '2px solid #ccc',
      borderRadius: '8px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
    }}>
      <h4>Friend Request Modal Test</h4>
      <div style={{ marginBottom: '10px' }}>
        <label>
          Request ID:
          <input
            type="text"
            value={testData.requestId}
            onChange={(e) => setTestData(prev => ({ ...prev, requestId: e.target.value }))}
            style={{ marginLeft: '5px', width: '150px' }}
          />
        </label>
      </div>
      <div style={{ marginBottom: '10px' }}>
        <label>
          From User ID:
          <input
            type="text"
            value={testData.fromUserId}
            onChange={(e) => setTestData(prev => ({ ...prev, fromUserId: e.target.value }))}
            style={{ marginLeft: '5px', width: '150px' }}
          />
        </label>
      </div>
      <button 
        onClick={handleOpenModal}
        style={{
          background: '#4CAF50',
          color: 'white',
          border: 'none',
          padding: '8px 16px',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Test Friend Request Modal
      </button>

      <FriendRequestModal
        isOpen={showModal}
        onClose={handleCloseModal}
        requestId={testData.requestId}
        fromUserId={testData.fromUserId}
        onRequestHandled={handleRequestHandled}
      />
    </div>
  );
}
