import React, { useState, useEffect } from 'react';
import { useAuth } from '../AuthContext';
import {
  sendFriendRequest,
  acceptFriendRequest,
  declineFriendRequest,
  cancelFriendRequest,
  removeFriend,
  getPendingFriendRequests,
  getUserFriends,
  searchUsers,
  subscribeToFriendRequests,
  subscribeToUserFriends,
  getComprehensiveFriendSuggestions,
  getMutualFriends
} from '../services/friends';
import PresenceIndicator, { PresenceStatus } from './PresenceIndicator';
import './FriendsManager.css';

/**
 * Friends Manager Component
 * Handles friend requests, friends list, and user search
 */
export default function FriendsManager({ onClose }) {
  const { currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState('friends');
  const [friends, setFriends] = useState([]);
  const [receivedRequests, setReceivedRequests] = useState([]);
  const [sentRequests, setSentRequests] = useState([]);
  const [searchResults, setSearchResults] = useState([]);
  const [suggestions, setSuggestions] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [searching, setSearching] = useState(false);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [actionLoading, setActionLoading] = useState({});

  useEffect(() => {
    if (!currentUser) return;

    const unsubscribers = [];

    // Subscribe to friends list
    unsubscribers.push(
      subscribeToUserFriends(currentUser.uid, (friendsList) => {
        setFriends(friendsList);
        setLoading(false);
      })
    );

    // Subscribe to received friend requests
    unsubscribers.push(
      subscribeToFriendRequests(currentUser.uid, setReceivedRequests, 'received')
    );

    // Subscribe to sent friend requests
    unsubscribers.push(
      subscribeToFriendRequests(currentUser.uid, setSentRequests, 'sent')
    );

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  }, [currentUser]);

  // Load friend suggestions
  useEffect(() => {
    if (!currentUser) return;

    const loadSuggestions = async () => {
      setLoadingSuggestions(true);
      try {
        const friendSuggestions = await getComprehensiveFriendSuggestions(currentUser.uid, 10);
        setSuggestions(friendSuggestions);
      } catch (error) {
        console.error('Error loading friend suggestions:', error);
      } finally {
        setLoadingSuggestions(false);
      }
    };

    loadSuggestions();
  }, [currentUser, friends.length]); // Reload when friends list changes

  const handleSearch = async () => {
    if (!searchTerm.trim() || searching) return;

    setSearching(true);
    try {
      console.log('🔍 FriendsManager: Starting search for:', searchTerm);
      const results = await searchUsers(searchTerm, currentUser.uid);
      console.log('🔍 FriendsManager: Search results:', results);
      setSearchResults(results);
    } catch (error) {
      console.error('❌ FriendsManager: Error searching users:', error);
      alert('Failed to search users. Please try again.');
    } finally {
      setSearching(false);
    }
  };

  // Debug function to test search functionality
  const handleDebugSearch = async () => {
    console.log('🐛 DEBUG: Testing search functionality');
    console.log('🐛 Current user:', currentUser.uid, currentUser.email);

    try {
      // Test basic database access
      console.log('🐛 Testing database access...');
      const testResults = await searchUsers('test', currentUser.uid);
      console.log('🐛 Test search results:', testResults);

      // Test specific email search if we have a search term
      if (searchTerm.includes('@')) {
        console.log('🐛 Testing email search for:', searchTerm);
        const emailResults = await searchUsers(searchTerm, currentUser.uid);
        console.log('🐛 Email search results:', emailResults);
      }
    } catch (error) {
      console.error('🐛 DEBUG: Search test failed:', error);
    }
  };

  const handleSendRequest = async (userId, message = '') => {
    setActionLoading(prev => ({ ...prev, [userId]: true }));
    try {
      await sendFriendRequest(currentUser.uid, userId, message);

      // Refresh search results to update status
      if (searchTerm.trim()) {
        const results = await searchUsers(searchTerm, currentUser.uid);
        setSearchResults(results);
      }

      // Refresh suggestions to remove the user we just sent a request to
      const updatedSuggestions = await getComprehensiveFriendSuggestions(currentUser.uid, 10);
      setSuggestions(updatedSuggestions);

    } catch (error) {
      console.error('Error sending friend request:', error);
      alert('Failed to send friend request: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  const handleAcceptRequest = async (requestId) => {
    setActionLoading(prev => ({ ...prev, [requestId]: true }));
    try {
      await acceptFriendRequest(requestId, currentUser.uid);
    } catch (error) {
      console.error('Error accepting friend request:', error);
      alert('Failed to accept friend request: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [requestId]: false }));
    }
  };

  const handleDeclineRequest = async (requestId) => {
    setActionLoading(prev => ({ ...prev, [requestId]: true }));
    try {
      await declineFriendRequest(requestId, currentUser.uid);
    } catch (error) {
      console.error('Error declining friend request:', error);
      alert('Failed to decline friend request: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [requestId]: false }));
    }
  };

  const handleCancelRequest = async (requestId) => {
    setActionLoading(prev => ({ ...prev, [requestId]: true }));
    try {
      await cancelFriendRequest(requestId, currentUser.uid);
    } catch (error) {
      console.error('Error canceling friend request:', error);
      alert('Failed to cancel friend request: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [requestId]: false }));
    }
  };

  const handleRemoveFriend = async (friendId) => {
    if (!confirm('Are you sure you want to remove this friend?')) return;

    setActionLoading(prev => ({ ...prev, [friendId]: true }));
    try {
      await removeFriend(currentUser.uid, friendId);
    } catch (error) {
      console.error('Error removing friend:', error);
      alert('Failed to remove friend: ' + error.message);
    } finally {
      setActionLoading(prev => ({ ...prev, [friendId]: false }));
    }
  };

  const renderFriendsList = () => (
    <div className="friends-list">
      {loading ? (
        <div className="loading-state">Loading friends...</div>
      ) : friends.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">👥</div>
          <h3>No friends yet</h3>
          <p>Start connecting with other community members!</p>
          <button 
            className="switch-tab-btn"
            onClick={() => setActiveTab('search')}
          >
            Find Friends
          </button>
        </div>
      ) : (
        <div className="friends-grid">
          {friends.map(friend => (
            <div key={friend.id} className="friend-card">
              <PresenceStatus
                userId={friend.id}
                userInfo={friend}
                showAvatar={true}
                showName={true}
                showStatus={true}
                showLastSeen={true}
                className="friend-presence"
              />
              <div className="friend-actions">
                <button 
                  className="message-friend-btn"
                  onClick={() => {/* TODO: Open messaging */}}
                >
                  💬 Message
                </button>
                <button 
                  className="remove-friend-btn"
                  onClick={() => handleRemoveFriend(friend.id)}
                  disabled={actionLoading[friend.id]}
                >
                  {actionLoading[friend.id] ? '⏳' : '❌'} Remove
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderFriendRequests = () => (
    <div className="friend-requests">
      <div className="requests-section">
        <h3>Received Requests ({receivedRequests.length})</h3>
        {receivedRequests.length === 0 ? (
          <div className="empty-requests">No pending requests</div>
        ) : (
          <div className="requests-list">
            {receivedRequests.map(request => (
              <div key={request.id} className="request-card">
                <PresenceStatus
                  userId={request.fromUserId}
                  userInfo={request.userInfo}
                  showAvatar={true}
                  showName={true}
                  showStatus={true}
                  className="request-user"
                />
                {request.message && (
                  <div className="request-message">"{request.message}"</div>
                )}
                <div className="request-actions">
                  <button 
                    className="accept-btn"
                    onClick={() => handleAcceptRequest(request.id)}
                    disabled={actionLoading[request.id]}
                  >
                    {actionLoading[request.id] ? '⏳' : '✅'} Accept
                  </button>
                  <button 
                    className="decline-btn"
                    onClick={() => handleDeclineRequest(request.id)}
                    disabled={actionLoading[request.id]}
                  >
                    {actionLoading[request.id] ? '⏳' : '❌'} Decline
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="requests-section">
        <h3>Sent Requests ({sentRequests.length})</h3>
        {sentRequests.length === 0 ? (
          <div className="empty-requests">No pending sent requests</div>
        ) : (
          <div className="requests-list">
            {sentRequests.map(request => (
              <div key={request.id} className="request-card">
                <PresenceStatus
                  userId={request.toUserId}
                  userInfo={request.userInfo}
                  showAvatar={true}
                  showName={true}
                  showStatus={true}
                  className="request-user"
                />
                {request.message && (
                  <div className="request-message">"{request.message}"</div>
                )}
                <div className="request-actions">
                  <button 
                    className="cancel-btn"
                    onClick={() => handleCancelRequest(request.id)}
                    disabled={actionLoading[request.id]}
                  >
                    {actionLoading[request.id] ? '⏳' : '🚫'} Cancel
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );

  const renderSuggestions = () => (
    <div className="friend-suggestions">
      <div className="suggestions-header">
        <h3>✨ People You May Know</h3>
        <p>Based on shared interests, mutual friends, and community activity</p>
      </div>

      {loadingSuggestions ? (
        <div className="loading-state">Loading suggestions...</div>
      ) : suggestions.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">🔍</div>
          <h3>No suggestions available</h3>
          <p>Try being more active in the community to get better suggestions!</p>
          <button
            className="switch-tab-btn"
            onClick={() => setActiveTab('search')}
          >
            Search for Friends
          </button>
        </div>
      ) : (
        <div className="suggestions-grid">
          {suggestions.map(user => (
            <SuggestionCard
              key={user.id}
              user={user}
              onSendRequest={handleSendRequest}
              actionLoading={actionLoading[user.id]}
            />
          ))}
        </div>
      )}
    </div>
  );

  const renderUserSearch = () => (
    <div className="user-search">
      <div className="search-header">
        <div className="search-input-container">
          <input
            type="text"
            placeholder="Search by name or email address..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            className="search-input"
          />
          <button
            onClick={handleSearch}
            disabled={searching || !searchTerm.trim()}
            className="search-btn"
          >
            {searching ? '⏳' : '🔍'}
          </button>

        </div>

        <div className="search-tips">
          <p><strong>💡 Search Tips:</strong></p>
          <ul>
            <li>Enter a complete email address to find specific users</li>
            <li>Search by name to find users by their display name</li>
            <li>Users must allow themselves to be found in search settings</li>
          </ul>
        </div>
      </div>

      <div className="search-results">
        {searchResults.length === 0 && searchTerm && !searching ? (
          <div className="empty-results">
            <div className="empty-icon">🔍</div>
            <p>No users found matching "{searchTerm}"</p>
          </div>
        ) : (
          <div className="users-grid">
            {searchResults.map(user => (
              <UserSearchCard
                key={user.id}
                user={user}
                onSendRequest={handleSendRequest}
                actionLoading={actionLoading[user.id]}
                onSwitchToRequests={() => setActiveTab('requests')}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="friends-manager" onClick={(e) => e.target === e.currentTarget && onClose()}>
      <div className="friends-content">
        <div className="friends-header">
          <h2>👥 Friends & Connections</h2>
          <button className="close-friends-btn" onClick={onClose}>✕</button>
        </div>

        <div className="friends-tabs">
          <button
            className={`tab-btn ${activeTab === 'friends' ? 'active' : ''}`}
            onClick={() => setActiveTab('friends')}
          >
            Friends ({friends.length})
          </button>
          <button
            className={`tab-btn ${activeTab === 'suggestions' ? 'active' : ''}`}
            onClick={() => setActiveTab('suggestions')}
          >
            Suggestions ({suggestions.length})
          </button>
          <button
            className={`tab-btn ${activeTab === 'requests' ? 'active' : ''}`}
            onClick={() => setActiveTab('requests')}
          >
            Requests ({receivedRequests.length + sentRequests.length})
          </button>
          <button
            className={`tab-btn ${activeTab === 'search' ? 'active' : ''}`}
            onClick={() => setActiveTab('search')}
          >
            Find Friends
          </button>
        </div>

        <div className="friends-body">
          {activeTab === 'friends' && renderFriendsList()}
          {activeTab === 'suggestions' && renderSuggestions()}
          {activeTab === 'requests' && renderFriendRequests()}
          {activeTab === 'search' && renderUserSearch()}
        </div>
      </div>
    </div>
  );
}

/**
 * User Search Card Component
 */
function UserSearchCard({ user, onSendRequest, actionLoading, onSwitchToRequests }) {
  const [showMessageInput, setShowMessageInput] = useState(false);
  const [message, setMessage] = useState('');

  const getRequestButtonText = () => {
    if (actionLoading) return '⏳';
    
    const status = user.friendRequestStatus;
    if (!status) return '➕ Add Friend';
    
    switch (status.status) {
      case 'friends': return '✅ Friends';
      case 'pending': 
        return status.fromUserId === user.id ? '⏳ Request Sent' : '📨 Respond';
      default: return '➕ Add Friend';
    }
  };

  const canSendRequest = () => {
    const status = user.friendRequestStatus;
    if (!status) return true; // No existing relationship - can send request
    if (status.status === 'friends') return false; // Already friends
    if (status.status === 'pending') {
      // If pending, check who sent the request
      // If current user sent it, can't do anything (button shows "Request Sent")
      // If other user sent it, can respond (button shows "Respond")
      return status.fromUserId !== currentUser.uid;
    }
    return true; // Default case
  };

  const handleSendRequest = () => {
    const status = user.friendRequestStatus;

    // If this is a "Respond" case (pending request from this user to current user)
    if (status && status.status === 'pending' && status.fromUserId === user.id) {
      // Switch to the requests tab to handle the response
      if (onSwitchToRequests) {
        onSwitchToRequests();
      } else {
        alert('Please go to the "Requests" tab to accept or decline this friend request.');
      }
      return;
    }

    // Normal send request flow
    if (showMessageInput) {
      onSendRequest(user.id, message);
      setMessage('');
      setShowMessageInput(false);
    } else {
      setShowMessageInput(true);
    }
  };

  return (
    <div className="user-search-card">
      <PresenceStatus
        userId={user.id}
        userInfo={user}
        showAvatar={true}
        showName={true}
        showStatus={true}
        className="search-user-info"
      />
      
      {user.bio && (
        <div className="user-bio">{user.bio}</div>
      )}

      {showMessageInput && (
        <div className="message-input-section">
          <input
            type="text"
            placeholder="Add a message (optional)"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="message-input"
            maxLength={100}
          />
          <div className="message-actions">
            <button onClick={handleSendRequest} className="send-request-btn">
              Send Request
            </button>
            <button 
              onClick={() => setShowMessageInput(false)} 
              className="cancel-message-btn"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {!showMessageInput && (
        <button 
          className={`friend-action-btn ${canSendRequest() ? 'primary' : 'disabled'}`}
          onClick={canSendRequest() ? handleSendRequest : undefined}
          disabled={actionLoading || !canSendRequest()}
        >
          {getRequestButtonText()}
        </button>
      )}
    </div>
  );
}

/**
 * Suggestion Card Component
 * Shows a suggested friend with reasons and relevance score
 */
function SuggestionCard({ user, onSendRequest, actionLoading }) {
  const [showMessageInput, setShowMessageInput] = useState(false);
  const [message, setMessage] = useState('');
  const [showMutualFriends, setShowMutualFriends] = useState(false);
  const [mutualFriends, setMutualFriends] = useState([]);
  const [loadingMutual, setLoadingMutual] = useState(false);

  const handleSendRequest = () => {
    if (showMessageInput) {
      onSendRequest(user.id, message);
      setMessage('');
      setShowMessageInput(false);
    } else {
      setShowMessageInput(true);
    }
  };

  const loadMutualFriends = async () => {
    if (showMutualFriends) {
      setShowMutualFriends(false);
      return;
    }

    setLoadingMutual(true);
    try {
      const { getMutualFriends } = await import('../services/friends');
      const mutual = await getMutualFriends(user.id, user.id); // This needs current user ID
      setMutualFriends(mutual);
      setShowMutualFriends(true);
    } catch (error) {
      console.error('Error loading mutual friends:', error);
    } finally {
      setLoadingMutual(false);
    }
  };

  const getSuggestionTypeIcon = () => {
    switch (user.suggestionType) {
      case 'profile': return '👤';
      case 'story': return '📖';
      case 'combined': return '⭐';
      default: return '✨';
    }
  };

  const getSuggestionTypeText = () => {
    switch (user.suggestionType) {
      case 'profile': return 'Profile Match';
      case 'story': return 'Story Interest';
      case 'combined': return 'Great Match';
      default: return 'Suggested';
    }
  };

  return (
    <div className="suggestion-card">
      <div className="suggestion-header">
        <div className="suggestion-type">
          <span className="suggestion-icon">{getSuggestionTypeIcon()}</span>
          <span className="suggestion-label">{getSuggestionTypeText()}</span>
        </div>
        {user.combinedScore && (
          <div className="relevance-score" title="Relevance Score">
            {Math.round(user.combinedScore)}%
          </div>
        )}
      </div>

      <PresenceStatus
        userId={user.id}
        userInfo={user}
        showAvatar={true}
        showName={true}
        showStatus={true}
        className="suggestion-user-info"
      />

      {user.bio && (
        <div className="suggestion-bio">{user.bio}</div>
      )}

      {/* Suggestion Reasons */}
      {user.suggestionReasons && user.suggestionReasons.length > 0 && (
        <div className="suggestion-reasons">
          <h5>Why we suggest this connection:</h5>
          <ul>
            {user.suggestionReasons.map((reason, index) => (
              <li key={index}>{reason}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Mutual Friends Button */}
      {user.suggestionReasons?.some(reason => reason.includes('mutual')) && (
        <button
          className="mutual-friends-btn"
          onClick={loadMutualFriends}
          disabled={loadingMutual}
        >
          {loadingMutual ? '⏳' : showMutualFriends ? '👥 Hide' : '👥 Show'} Mutual Friends
        </button>
      )}

      {/* Mutual Friends List */}
      {showMutualFriends && (
        <div className="mutual-friends-list">
          {mutualFriends.length > 0 ? (
            mutualFriends.map(friend => (
              <div key={friend.id} className="mutual-friend-item">
                <span className="mutual-friend-avatar">{friend.avatar || '👤'}</span>
                <span className="mutual-friend-name">{friend.name || 'Community Member'}</span>
              </div>
            ))
          ) : (
            <div className="no-mutual-friends">No mutual friends found</div>
          )}
        </div>
      )}

      {/* Message Input */}
      {showMessageInput && (
        <div className="suggestion-message-input">
          <input
            type="text"
            placeholder="Add a personal message (optional)"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="suggestion-message-field"
            maxLength={100}
          />
          <div className="suggestion-message-actions">
            <button onClick={handleSendRequest} className="send-suggestion-request-btn">
              Send Request
            </button>
            <button
              onClick={() => setShowMessageInput(false)}
              className="cancel-suggestion-btn"
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Action Button */}
      {!showMessageInput && (
        <button
          className="suggestion-action-btn"
          onClick={handleSendRequest}
          disabled={actionLoading}
        >
          {actionLoading ? '⏳ Sending...' : '➕ Connect'}
        </button>
      )}
    </div>
  );
}
